#!/bin/bash

# Script de Deploy para Google Cloud Run
# Uso: ./deploy-cloud-run.sh [PROJECT_ID] [REGION] [SERVICE_NAME]

set -e

# Configurações padrão
DEFAULT_PROJECT_ID="seu-project-id"
DEFAULT_REGION="us-central1"
DEFAULT_SERVICE_NAME="super-gateway"

# Parâmetros
PROJECT_ID=${1:-$DEFAULT_PROJECT_ID}
REGION=${2:-$DEFAULT_REGION}
SERVICE_NAME=${3:-$DEFAULT_SERVICE_NAME}

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Deploy para Google Cloud Run${NC}"
echo -e "${BLUE}Projeto: ${PROJECT_ID}${NC}"
echo -e "${BLUE}Região: ${REGION}${NC}"
echo -e "${BLUE}Serviço: ${SERVICE_NAME}${NC}"
echo ""

# Verificar se gcloud está instalado
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ Google Cloud SDK não está instalado${NC}"
    echo "Instale em: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Verificar se está logado
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo -e "${YELLOW}⚠️  Não está logado no Google Cloud${NC}"
    gcloud auth login
fi

# Configurar projeto
echo -e "${BLUE}🔧 Configurando projeto...${NC}"
gcloud config set project $PROJECT_ID
gcloud config set run/region $REGION

# Verificar se o projeto existe
if ! gcloud projects describe $PROJECT_ID &> /dev/null; then
    echo -e "${RED}❌ Projeto ${PROJECT_ID} não encontrado${NC}"
    exit 1
fi

# Habilitar APIs necessárias
echo -e "${BLUE}🔌 Habilitando APIs...${NC}"
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com

# Build e push da imagem
echo -e "${BLUE}🏗️  Fazendo build da imagem...${NC}"
IMAGE_TAG="gcr.io/${PROJECT_ID}/${SERVICE_NAME}:latest"

# Verificar qual Dockerfile usar (em ordem de preferência)
DOCKERFILES=("Dockerfile.ultra-simple" "Dockerfile.simple" "Dockerfile" "Dockerfile.working" "Dockerfile.minimal")
DOCKERFILE_PATH=""

for dockerfile in "${DOCKERFILES[@]}"; do
    if [ -f "apps/web/$dockerfile" ]; then
        DOCKERFILE_PATH="apps/web/$dockerfile"
        echo -e "${GREEN}✅ Dockerfile encontrado: $dockerfile${NC}"
        break
    fi
done

if [ -z "$DOCKERFILE_PATH" ]; then
    echo -e "${RED}❌ Nenhum Dockerfile encontrado em apps/web/${NC}"
    echo "Dockerfiles disponíveis:"
    ls -la apps/web/Dockerfile*
    exit 1
fi

echo -e "${BLUE}📦 Usando Dockerfile: ${DOCKERFILE_PATH}${NC}"

# Usar Cloud Build para build
echo -e "${BLUE}📦 Build com Cloud Build...${NC}"
gcloud builds submit --tag $IMAGE_TAG --file $DOCKERFILE_PATH .

# Deploy no Cloud Run
echo -e "${BLUE}🚀 Fazendo deploy no Cloud Run...${NC}"
gcloud run deploy $SERVICE_NAME \
    --image $IMAGE_TAG \
    --platform managed \
    --region $REGION \
    --allow-unauthenticated \
    --port 3000 \
    --memory 2Gi \
    --cpu 2 \
    --max-instances 10 \
    --min-instances 0 \
    --set-env-vars NODE_ENV=production \
    --timeout 300 \
    --cpu-throttling

# Obter URL do serviço
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)")

echo ""
echo -e "${GREEN}✅ Deploy concluído com sucesso!${NC}"
echo -e "${GREEN}🌐 URL: ${SERVICE_URL}${NC}"
echo ""

# Verificar status
echo -e "${BLUE}📊 Status do serviço:${NC}"
gcloud run services describe $SERVICE_NAME --region=$REGION --format="table(status.conditions[0].type,status.conditions[0].status,status.conditions[0].message)"

echo ""
echo -e "${YELLOW}⚠️  IMPORTANTE: Configure as variáveis de ambiente necessárias:${NC}"
echo "gcloud run services update $SERVICE_NAME --set-env-vars \\"
echo "  DATABASE_URL=\"sua_url_do_banco\",\\"
echo "  NEXTAUTH_SECRET=\"seu_secret\",\\"
echo "  NEXTAUTH_URL=\"$SERVICE_URL\",\\"
echo "  NEXTAUTH_URL_INTERNAL=\"$SERVICE_URL\""

echo ""
echo -e "${BLUE}📚 Para mais informações, consulte: cloud-run-deploy.md${NC}"
