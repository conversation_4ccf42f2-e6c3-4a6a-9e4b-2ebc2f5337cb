"use client";
import { config } from "@repo/config";
import { useSession } from "@saas/auth/hooks/use-session";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { UserMenu } from "@saas/shared/components/UserMenu";
import { Logo } from "@shared/components/Logo";
import { cn } from "@ui/lib";
import {
		BotMessageSquareIcon,
		ChevronRightIcon,
		HomeIcon,
		SettingsIcon,
		UserCog2Icon,
		UserCogIcon,
		DollarSignIcon,
		BuildingIcon,
		ShieldIcon,
		ShoppingCartIcon,
		PackageIcon,
		TrendingUpIcon,
		CreditCardIcon,
		UsersIcon,
		BarChart3Icon,
		WebhookIcon,
		ActivityIcon,
		FileTextIcon,
		GlobeIcon,
		ZapIcon,
		StoreIcon,
		GiftIcon,
		ChartSpline,
		UserIcon,
		BotIcon,
		MessageCircle,
	} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useTranslations } from "next-intl";
import { OrganzationSelect } from "../../organizations/components/OrganizationSelect";
import { DashIcon } from "@radix-ui/react-icons";

interface MenuItem {
	label: string;
	href: string;
	icon: React.ComponentType<{ className?: string }>;
	isActive: boolean;
	isSecondary?: boolean;
}

export function NavBar() {
	const t = useTranslations();
	const pathname = usePathname();
	const { user } = useSession();
	const { activeOrganization } = useActiveOrganization();

	const { useSidebarLayout } = config.ui.saas;
	const isAdminRoute = pathname.startsWith("/app/backoffice");

	const basePath = activeOrganization
		? `/app/${activeOrganization.slug}`
		: "/app";

	// Menu simplificado para rotas de admin (foco em gestão)
	const adminMenuItems: MenuItem[] = [
		{
			label: "Dashboard",
			href: "/app/backoffice",
			icon: HomeIcon,
			isActive: pathname === "/app/backoffice",
		},
		{
			label: "Organizações",
			href: "/app/backoffice/organizations",
			icon: BuildingIcon,
			isActive: pathname.startsWith("/app/backoffice/organizations"),
		},
		{
			label: "Usuários",
			href: "/app/backoffice/users",
			icon: UsersIcon,
			isActive: pathname.startsWith("/app/backoffice/users"),
		},
		{
			label: "Transações",
			href: "/app/backoffice/transactions",
			icon: DollarSignIcon,
			isActive: pathname.startsWith("/app/backoffice/transactions"),
		},
		{
			label: "Integrações",
			href: "/app/backoffice/integrations",
			icon: WebhookIcon,
			isActive: pathname.startsWith("/app/backoffice/integrations"),
		},
		{
			label: "Configurações",
			href: "/app/backoffice/settings",
			icon: SettingsIcon,
			isActive: pathname.startsWith("/app/backoffice/settings"),
		},
	];

	// Menu atualizado para rotas de tenant baseado na estrutura real de pastas
	const tenantMenuItems: MenuItem[] = [
		{
			label: "Dashboard",
			href: basePath,
			icon: ChartSpline,
			isActive: pathname === basePath,
		},
		{
			label: "Vendas",
			href: `${basePath}/sales`,
			icon: ShoppingCartIcon,
			isActive: pathname.includes("/sales"),
		},
		{
			label: "Produtos",
			href: `${basePath}/products`,
			icon: PackageIcon,
			isActive: pathname.includes("/products"),
		},
		{
			label: "Finanças",
			href: `${basePath}/finance`,
			icon: DollarSignIcon,
			isActive: pathname.includes("/finance"),
		},
		{
			label: "Clientes",
			href: `${basePath}/customers`,
			icon: UserIcon,
			isActive: pathname.includes("/customers"),
		},
		{
			label: "Analytics",
			href: `${basePath}/analytics`,
			icon: BarChart3Icon,
			isActive: pathname.includes("/analytics"),
		},
		{
			label: "Integrações",
			href: `${basePath}/integrations`,
			icon: ZapIcon,
			isActive: pathname.includes("/integrations"),
		},
		{
			label: "Chatbot IA",
			href: `${basePath}/chatbot`,
			icon: BotIcon,
			isActive: pathname.includes("/chatbot"),
		},
		{
			label: "Suporte",
			href: `${basePath}/support`,
			icon: MessageCircle,
			isActive: pathname.includes("/support"),
		},
		// Separador visual
		{
			label: "Cobrança",
			href: `${basePath}/usage-billing`,
			icon: CreditCardIcon,
			isActive: pathname.includes("/usage-billing"),
			isSecondary: true,
		},
		...(activeOrganization && !config.organizations.hideOrganization
			? [
					{
						label: t("app.menu.organizationSettings"),
						href: `${basePath}/settings`,
						icon: SettingsIcon,
						isActive: pathname.startsWith(`${basePath}/settings`),
						isSecondary: true,
					},
				]
			: []),
		{
			label: t("app.menu.accountSettings"),
			href: "/app/account/settings",
			icon: UserCog2Icon,
			isActive: pathname.startsWith("/app/account/settings"),
			isSecondary: true,
		},
	];

	// Menu ativo baseado na rota
	const activeMenuItems = isAdminRoute ? adminMenuItems : tenantMenuItems;

	return (
		<nav
			className={cn("w-full", {
				"w-full md:fixed md:top-0 md:left-0 md:h-full md:w-[280px]":
					useSidebarLayout,
			})}
		>
			<div
				className={cn("container max-w-6xl py-4", {
					"container max-w-6xl py-4 md:flex md:h-full md:flex-col md:px-4 md:pt-6 md:pb-0":
						useSidebarLayout,
				})}
			>
				<div className="flex flex-wrap items-center justify-between gap-4">
					<div
						className={cn("flex items-center gap-4 md:gap-2", {
							"md:flex md:w-full md:flex-col md:items-stretch md:align-stretch":
								useSidebarLayout,
						})}
					>
						<Link href="/app" className="block">
							<Logo />
						</Link>

						{/* Indicador de modo admin */}
						{isAdminRoute && (
							<div className="flex items-center gap-2 px-2 py-1 rounded-full bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200 text-xs font-medium">
								<ShieldIcon className="h-3 w-3" />
								Admin
							</div>
						)}

						{/* Seletor de organização apenas para rotas de tenant */}
						{!isAdminRoute &&
							config.organizations.enable &&
							!config.organizations.hideOrganization && (
								<>
									<span
										className={cn(
											"hidden opacity-30 md:block",
											{
												"md:hidden": useSidebarLayout,
											},
										)}
									>
										<ChevronRightIcon className="size-4" />
									</span>

									<OrganzationSelect
										className={cn({
											"md:-mx-1 md:mt-2":
												useSidebarLayout,
										})}
									/>
								</>
							)}
					</div>

					<div
						className={cn(
							"mr-0 ml-auto flex items-center justify-end gap-4",
							{
								"md:hidden": useSidebarLayout,
							},
						)}
					>
						<UserMenu />
					</div>
				</div>

				<div
					className={cn(
						"no-scrollbar -mx-4 -mb-4 mt-6 flex list-none items-center justify-start gap-4 overflow-x-auto px-4 text-sm",
						{
							"md:mx-0 md:my-4 md:flex md:flex-col md:items-stretch md:gap-0 md:px-0 md:overflow-y-auto md:flex-1":
								useSidebarLayout,
						},
					)}
				>
					{useSidebarLayout ? (
						// Layout de sidebar simplificado
						<div className="space-y-2">
							{/* Menu principal */}
							<div className="space-y-1">
								{activeMenuItems
									.filter(item => !item.isSecondary)
									.map((menuItem) => (
										<Link
											key={menuItem.href}
											href={menuItem.href}
											className={cn(
												"flex items-center gap-3 px-4 py-3 text-sm font-medium rounded-lg mx-1 transition-all duration-200 group",
												menuItem.isActive
													? "bg-primary text-primary-foreground shadow-sm"
													: "text-muted-foreground hover:text-foreground hover:bg-muted/50"
											)}
											prefetch
										>
											<menuItem.icon className={cn(
												"size-4 shrink-0 transition-transform duration-200",
												menuItem.isActive ? "" : "group-hover:scale-110"
											)} />
											<span className="font-medium">{menuItem.label}</span>
										</Link>
									))}
							</div>

							{/* Separador visual */}
							{activeMenuItems.some(item => item.isSecondary) && (
								<div className="px-4 py-2">
									<div className="h-px bg-border" />
								</div>
							)}

							{/* Menu secundário */}
							{activeMenuItems.some(item => item.isSecondary) && (
								<div className="space-y-1">
									{activeMenuItems
										.filter(item => item.isSecondary)
										.map((menuItem) => (
											<Link
												key={menuItem.href}
												href={menuItem.href}
												className={cn(
													"flex items-center gap-3 px-4 py-3 text-sm font-medium rounded-lg mx-1 transition-all duration-200 group text-muted-foreground hover:text-foreground hover:bg-muted/50",
													menuItem.isActive && "bg-muted text-foreground"
												)}
												prefetch
											>
												<menuItem.icon className="size-4 shrink-0 transition-transform duration-200 group-hover:scale-110" />
												<span className="font-medium">{menuItem.label}</span>
											</Link>
										))}
								</div>
							)}
						</div>
					) : (
						// Layout horizontal simplificado
						activeMenuItems
							.filter(item => !item.isSecondary)
							.map((menuItem) => (
								<div key={menuItem.href} className="flex-shrink-0">
									<Link
										href={menuItem.href}
										className={cn(
											"flex items-center gap-2 whitespace-nowrap border-b-2 px-1 pb-3",
											menuItem.isActive
												? "border-primary font-bold"
												: "border-transparent"
										)}
										prefetch
									>
										<menuItem.icon
											className={`size-4 shrink-0 ${
												menuItem.isActive
													? "text-primary"
													: "opacity-50"
											}`}
										/>
										<span>{menuItem.label}</span>
									</Link>
								</div>
							))
					)}

					{/* Botão para alternar entre admin e tenant */}
					{user?.role === "admin" && (
						<div className={cn("flex-shrink-0", {
							"mt-auto mb-4": useSidebarLayout
						})}>
							<Link
								href={isAdminRoute ? "/app" : "/app/backoffice"}
								className={cn(
									"flex items-center gap-2 whitespace-nowrap border-b-2 px-1 pb-3 border-transparent hover:border-primary/50 transition-colors",
									{
										"border-b-0 px-4 py-2 mx-1 rounded-lg hover:bg-muted text-muted-foreground hover:text-foreground":
											useSidebarLayout,
									},
								)}
							>
								{isAdminRoute ? (
									<>
										<BuildingIcon className="size-4 shrink-0" />
										<span>Voltar ao App</span>
									</>
								) : (
									<>
										<ShieldIcon className="size-4 shrink-0" />
										<span>Admin</span>
									</>
								)}
							</Link>
						</div>
					)}
				</div>

				<div
					className={cn(
						"-mx-4 md:-mx-4 mt-auto mb-0 hidden p-4 md:p-4",
						{
							"md:block": useSidebarLayout,
						},
					)}
				>
					<UserMenu showUserName />
				</div>
			</div>
		</nav>
	);
}
